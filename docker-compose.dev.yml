version: '3.8'

services:
  cssample-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: cssample-app-dev
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - DOTNET_USE_POLLING_FILE_WATCHER=true
      - DOTNET_RUNNING_IN_CONTAINER=true
      - ConnectionStrings__DefaultConnection=Server=db;Database=CSsampleDB_Dev;User=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
    volumes:
      # Source code for hot reload - mount entire project
      - ./CSsample:/src/CSsample
      # NuGet packages cache
      - nuget-cache:/root/.nuget/packages
      # Application logs
      - ./logs:/src/logs
      # Application data
      - ./data:/src/data
      # File uploads
      - ./uploads:/src/uploads
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - cssample-network

  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: cssample-db-dev
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1434:1433"
    volumes:
      # Database data persistence
      - db_data_dev:/var/opt/mssql
      # Database backups
      - ./db_backups:/var/backups
      # Database initialization scripts
      - ./db_scripts:/var/opt/mssql/scripts
    restart: unless-stopped
    networks:
      - cssample-network

volumes:
  db_data_dev:
    driver: local
  nuget-cache:
    driver: local

networks:
  cssample-network:
    driver: bridge

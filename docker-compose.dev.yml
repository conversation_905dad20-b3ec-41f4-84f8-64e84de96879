version: '3.8'

services:
  cssample-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: build  # Use build stage for development
    container_name: cssample-app-dev
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
      - ConnectionStrings__DefaultConnection=Server=db;Database=CSsampleDB_Dev;User=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
    volumes:
      # Source code for hot reload
      - ./CSsample:/src/CSsample
      # Application logs
      - ./logs:/app/logs
      # Application data
      - ./data:/app/data
      # File uploads
      - ./uploads:/app/uploads
      # HTTPS certificate
      - ~/.aspnet/https:/https:ro
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - cssample-network
    command: ["dotnet", "watch", "run", "--project", "/src/CSsample"]

  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: cssample-db-dev
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1434:1433"
    volumes:
      # Database data persistence
      - db_data_dev:/var/opt/mssql
      # Database backups
      - ./db_backups:/var/backups
      # Database initialization scripts
      - ./db_scripts:/var/opt/mssql/scripts
    restart: unless-stopped
    networks:
      - cssample-network

volumes:
  db_data_dev:
    driver: local

networks:
  cssample-network:
    driver: bridge

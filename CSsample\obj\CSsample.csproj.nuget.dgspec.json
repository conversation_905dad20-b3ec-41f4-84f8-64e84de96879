{"format": 1, "restore": {"/src/CSsample/CSsample.csproj": {}}, "projects": {"/src/CSsample/CSsample.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/src/CSsample/CSsample.csproj", "projectName": "CSsample", "projectPath": "/src/CSsample/CSsample.csproj", "packagesPath": "/root/.nuget/packages/", "outputPath": "/src/CSsample/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/root/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.413/PortableRuntimeIdentifierGraph.json"}}}}}
version: '3.8'

services:
  cssample-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cssample-app
    ports:
      - "8080:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=db;Database=CSsampleDB;User=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
    volumes:
      # Application logs
      - ./logs:/app/logs
      # Application data
      - ./data:/app/data
      # File uploads
      - ./uploads:/app/uploads
      # Configuration files (if needed)
      - ./config:/app/config:ro
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - cssample-network

  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: cssample-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      # Database data persistence
      - db_data:/var/opt/mssql
      # Database backups
      - ./db_backups:/var/backups
    restart: unless-stopped
    networks:
      - cssample-network

volumes:
  db_data:
    driver: local

networks:
  cssample-network:
    driver: bridge

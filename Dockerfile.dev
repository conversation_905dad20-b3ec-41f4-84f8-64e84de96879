# Development Dockerfile with Hot Reload support
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS development

WORKDIR /src

# Install dotnet-ef tool for database migrations
RUN dotnet tool install --global dotnet-ef
ENV PATH="$PATH:/root/.dotnet/tools"

# Copy project file and restore dependencies
COPY CSsample/*.csproj ./CSsample/
RUN dotnet restore CSsample/CSsample.csproj

# Set environment variables for development
ENV ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_URLS=http://+:80
ENV DOTNET_USE_POLLING_FILE_WATCHER=true
ENV DOTNET_RUNNING_IN_CONTAINER=true

# Create directories for volumes
RUN mkdir -p /src/logs /src/data /src/uploads

# Expose port
EXPOSE 80

# Set working directory to project folder
WORKDIR /src/CSsample

# Use dotnet watch for hot reload
CMD ["dotnet", "watch", "run", "--urls", "http://0.0.0.0:80"]
